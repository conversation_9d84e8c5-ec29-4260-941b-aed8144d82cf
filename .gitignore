# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

/db
/db/
*.db
nodes/sandbox/bin
db/gauss.db
gauss.db
*.log
.log

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# nodes sandbox bin directory
nodes/sandbox/bin/

# db folder
/db/
*.db

out.log
