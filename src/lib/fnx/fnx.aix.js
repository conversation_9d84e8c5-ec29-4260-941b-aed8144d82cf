const GoogleGenerativeAI = require('@google/generative-ai').GoogleGenerativeAI;
var fnxDb = require('./fnx.db');
var ps = require('ps-node');
var kill = require('tree-kill');
var fs = require('fs')
const fss = require('fs').promises;
const shell = require('shelljs');
const { exec } = require('child_process');
const path = require('path');
const cronstrue = require('cronstrue');
const Binance = require('node-binance-api');
const childProcess = require('child_process');
var spawn = require('child_process').spawn;
// var execfile = require('child_process').execFile
const { v4: uuidv4 } = require('uuid');
const modeDebug = true;
const fnxCore = require('./fnx.core');
const rulesetparams = require('../battle.ruleset.params');

const Redis = require('ioredis');
const redixPrefix = require('../redis.prefix.js');
const { default: sqlTradesClient } = require('../sqltradeslite');

const fnx = require('../../../nodes/_.functions.js')
const fnxIdx = require('../../../nodes/_.functions.indicators.js');
const fnxDex = require('../../../nodes/_.functions.dex.js');
const battleExchangeInfo = require('../battle.exchangeinfo.json');

const cronParser = require('cron-parser');
const timestamp = exports.timestamp = () => `[${new Date().toUTCString()}]`
const log = exports.log = (...args) => console.log(timestamp(), ...args);
const sleep = exports.sleep = (ms = 300) => { return new Promise(resolve => setTimeout(resolve, ms)); }

const dexSync_init_checkPosTPLs = true;
const commissionRate = 0.0004;
const mongoDbCollections = {
    dbName: 'algoweb',
    dbCollections: {
        users: 'app.users',
        userotps: 'app.users.otp',
        strategies: 'gauss.strategies',
    }
}
const redisKeys = {
    battle_init: 'battle_init',
};
const mongox = exports.mongox = {
    prompts: {
        list: prx => {
            const { redisClient, dbConnMongo, sqlClient, sqlTradesClient, slug, query, body } = prx;
            return new Promise(async (resolve, reject) => {
                try {
                    if (dbConnMongo) {
                        const type = query.type || null;
                        const db = dbConnMongo.db(mongoDbCollections.dbName)
                        const coll = db.collection('gauss.ai.prompts');
                        const q = {};
                        q.is_deleted = false;
                        if (query.user) {
                            q.user = `'${'query.user'}'`;
                        };
                        if (type) {
                            q.category =`${type}`;
                        }
                        var qStr = { $match: q };
                        var project = { $project: { _id: 0, prompt: 1, promptID: 1, title: 1, dtCreated: 1 } };
                        var qq = [qStr, project];
                        // console.log('q', qq)
                        var prompts = await coll.aggregate(qq).toArray();
                        // let prompts = [];
                        resolve(prompts)
                    } else {
                        reject('no dbConnMongo')
                    }
                } catch (e) {
                    console.log('err fnxAi: prompts - ', e)
                    reject(e);
                }
            });
        },
        update: prx => {
            const { redisClient, dbConnMongo, sqlClient, slug, query, body } = prx;
            return new Promise(async (resolve, reject) => {
                try {
                    if (dbConnMongo) {
                        const db = dbConnMongo.db(mongoDbCollections.dbName)
                        const coll = db.collection('gauss.ai.prompts');

                        const { promptID, title, prompt, isUpdate, type = null } = body;

                        if (isUpdate) {
                            // Update existing prompt
                            const result = await coll.updateOne(
                                { promptID: promptID },
                                {
                                    $set: {
                                        title: title,
                                        prompt: prompt,
                                        category: type,
                                        dtUpdated: new Date()
                                    }
                                }
                            );
                            resolve(result);
                        } else {
                            // Create new prompt
                            const newPrompt = {
                                promptID: `${Date.now()}`,
                                title: title,
                                prompt: prompt,
                                category: type,
                                dtCreated: new Date(),
                                dtUpdated: new Date(),
                                is_deleted: false
                            };
                            const result = await coll.insertOne(newPrompt);
                            resolve(result);
                        }
                    } else {
                        reject('no dbConnMongo')
                    }
                } catch (e) {
                    console.log('err fnxAi: update prompt - ', e)
                    reject(e);
                }
            });
        },
        delete: prx => {
            const { redisClient, dbConnMongo, sqlClient, slug, query, body } = prx;
            return new Promise(async (resolve, reject) => {
                try {
                    if (dbConnMongo) {
                        const db = dbConnMongo.db(mongoDbCollections.dbName)
                        const coll = db.collection('gauss.ai.prompts');

                        const { promptID } = body;

                        // Mark as deleted instead of actually deleting
                        const result = await coll.updateOne(
                            { promptID: promptID },
                            {
                                $set: {
                                    is_deleted: true,
                                    dtUpdated: new Date()
                                }
                            }
                        );
                        resolve(result);
                    } else {
                        reject('no dbConnMongo')
                    }
                } catch (e) {
                    console.log('err fnxAi: delete prompt - ', e)
                    reject(e);
                }
            });
        }
    },
    dataschemes: {
        list: prx => {
            const { redisClient, dbConnMongo, sqlClient, sqlTradesClient, slug, query, body } = prx;
            return new Promise(async (resolve, reject) => {
                try {
                    if (dbConnMongo) {
                        const db = dbConnMongo.db(mongoDbCollections.dbName)
                        const coll = db.collection('gauss.ai.prompts_dataSchemas');
                        const q = {};
                        q.is_deleted = false;
                        if (query.user) {
                            q.user = `'${'query.user'}'`;
                        };
                        var qStr = { $match: q };
                        var project = { $project: { _id: 0, semaID: 1, title: 1, sema: 1, category: 1, dtCreated: 1 } };
                        var qq = [qStr, project];
                        var prompts = await coll.aggregate(qq).toArray();
                        // let prompts = [];
                        resolve(prompts)
                    } else {
                        reject('no dbConnMongo')
                    }
                } catch (e) {
                    console.log('err fnxAi: dataschemes - list', e)
                    reject(e);
                }
            });
        },
    },
};

const redix = exports.redix = {
    pairklinedata : async prx => {
        const { redisClient, slug, query, body } = prx;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                let redisKey = redixPrefix.dataKline + slug[1];
                let redisKeyTicker = redixPrefix.dataKlineTicker + slug[1];
                // console.log('redisKey', redisKey, redixPrefix.dataKline, slug)
                let redisValStg = await redisClient.get(redisKey);
                let redisTickerValStg = await redisClient.get(redisKeyTicker);

                try {
                    redisValStg = JSON.parse(redisValStg);
                }
                catch (e) {}

                try {
                    redisTickerValStg = JSON.parse(redisTickerValStg);
                } catch (e) {};
                if (redisTickerValStg && redisTickerValStg?.k) {
                    let sonKlineTime = redisValStg.slice(-1)[0][0];
                    let tickerTime = redisTickerValStg.k.t;
                    let tickerInterval = redisTickerValStg.k.i;
                    let intervalMs = intervalToMilliseconds(tickerInterval);
                    redisTickerValStg.k.E = redisTickerValStg.E;
                    if (tickerTime - sonKlineTime <= intervalMs) {
                        // Find if there's a kline with the same time as the ticker
                        let existingIndex = redisValStg.findIndex(kline => kline[0] === tickerTime);
                        if (existingIndex !== -1) {
                            redisValStg[existingIndex] = KlineTicker(redisTickerValStg.k, 'array');
                        } else if (tickerTime > sonKlineTime) {
                            // Append ticker data as new kline
                            redisValStg.push(KlineTicker(redisTickerValStg.k, 'array'));
                        }
                    }
                }

                let limit = query.limit || 200 ;
                let resp;
                if (query.json) {
                    resp = redisValStg.map(a => klineArr2Json(a));
                } else {
                    resp = redisValStg;
                }
                resp = resp.slice(-limit);
                let sure = Date.now() - dt
                resolve({ klines: resp, sure });
            }
            catch (e) {
                console.log('redis pairklinedata error', e)
                reject({ data: false });
            }
        });
    }   
}

const ollama = exports.ollama = {
    models : async prx => {
        const { redisClient, slug, query, body } = prx;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                // Ollama API'sinden model listesini çek
                const response = await fetch('http://localhost:11434/api/tags');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                const models = data.models || [];
                resolve(models);
    // resolve({ klines: resp, sure });
            }
            catch (e) {
                console.log('ollama models error', e)
                reject({ data: false });
            }
        });
    },
    query : async prx => {
        const { redisClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            var dtBOP = Date.now();
            var model = body.model?.model || 'gemma3:1b"';
            const prompt = body.prompt;
            console.log('modelQueries - ollama: Querying with model:', model, prompt);
            const uri = 'http://127.0.0.1:11434/api/generate';
            if (!prompt) {
                console.error('modelQueries - ollama: Error no prompt', prompt);
                return null;
            }
            try {
                const response = await fetch(uri, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model,
                        prompt,
                        stream: false, // We want a single response
                    }),
                });
                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('modelQueries Ollama query error:', errorData);
                    return null;
                }
                const data = await response.json();
                console.log('modelResponse', Date.now() - dtBOP, data.response);
                const modelResponse = data.response;
                resolve({
                    elapsed: Date.now() - dtBOP,
                    aiResponse: modelResponse,
                });
            } catch (error) {
                console.error('Error fetching metadata from Ollama:', error);
                reject(e.message);
            }
        });
    },
    query_gemini: async prx => {
        const { redisClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            var dtBOP = Date.now();
            const prompt = body.prompt;
            if (!prompt) {
                console.error('modelQueries - query_gemini: Error no prompt', prompt);
                reject('no prompt');
            }
            try {
                const modelName = 'gemini-2.5-flash-lite'; // Specify the model you want to use
                const genAI = new GoogleGenerativeAI(process.env.GEMINI);
                const genAIModel = genAI.getGenerativeModel({ model: modelName });
                const result = await genAIModel.generateContent(prompt);
                const modelResponse = result.response.text().trim();
                console.log('modelResponse', Date.now() - dtBOP);
                try {
                    //model response ve prompt u bir dosyaya yaz timestamp ile.
                    let fileName = 'gemini_' + Date.now() + '.json';
                    let saveData = {
                        prompt,
                        response: modelResponse,
                        dt: new Date(Date.now()).toISOString(),
                        elapsed: Date.now() - dtBOP,
                    };
                    await fnxCore.main.save2file(saveData, fileName, true);
                    console.log('modelResponse saved to', fileName);
                } catch (e) {
                    console.log('modelResponse save error', e)
                }

                resolve({
                    elapsed: Date.now() - dtBOP,
                    aiResponse: modelResponse,
                });
            } catch (error) {
                console.error('Error fetching metadata from Ollama:', error);
                reject(error.message);
            }
        });
    },
}

const sqlite = exports.sqlite = {   
    list: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try {   
                let qTxt = `SELECT name
                            FROM sqlite_schema
                            WHERE   type ='table' 
                                    AND
                                    name NOT LIKE 'sqlite_%'
                                    ;`;
                var query = sqlClient.prepare(qTxt).all()
                resolve(query);
            } catch (e) {
                console.log('generic time e', e)
            }
        });
    },
    savedata: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            const valuez = JSON.parse(body);
            const dataName = (valuez.dataName) || '';
            const dataValue = typeof valuez.dataValue === 'string' ? valuez.dataValue : JSON.stringify(valuez.dataValue) || '';
            try {
                // Check if sandbox_selectedPair table exists
                let checkTableQuery = `SELECT name FROM sqlite_master WHERE type='table' AND name='sandbox_datax';`;
                let tableExists = sqlClient.prepare(checkTableQuery).get();
                
                // If table doesn't exist, create it
                if (!tableExists) {
                    // console.log('table not exists')
                    let createTableQuery = `
                        CREATE TABLE sandbox_datax (
                            dataName TEXT ,
                            dataValues TEXT ,
                            dtCreated datetime DEFAULT CURRENT_TIMESTAMP,
                            is_deleted boolean DEFAULT false
                        );
                    `;
                    sqlClient.exec(createTableQuery);
                } else {
                    // console.log('table exists')
                }

                //dataName e sahip daha oncesınden bir kayıt varsa onları delete et.
                //gercekten satir olarak sil
                let deleteQuery = `
                        DELETE FROM sandbox_datax
                        WHERE dataName = ?
                        ;
                    `;
                sqlClient.prepare(deleteQuery).run(dataName);
                
                let upsertQuery = `
                        INSERT INTO sandbox_datax (dataName, dataValues, dtCreated, is_deleted)
                        VALUES (?, ?, CURRENT_TIMESTAMP, false)
                        ;
                    `;

                dataName.length > 0 && dataValue.length > 0 && sqlClient.prepare(upsertQuery).run(dataName, dataValue);
                
                resolve({ success: true, message: 'Data saved successfully' });
            } catch (e) {
                console.log('Error saving data:', e);
                reject(e);
            }
        });
    },
};      

const anomali = exports.anomali = {
    runScript: (scriptPath, params, callback, io) => {
        // keep track of whether callback has been invoked to prevent multiple invocations
        var invoked = false;
        var child = childProcess.fork(scriptPath, [params]);

        child.on('message', function (message) {
            console.log('process2 Message from Child process : ' + message);
        });

        child.on('exit', function (code) {
            console.log(`child exiting: code: `, code);
            callback(code);
            // do some cleanup
        })
        child.on('error', function (err) {
            console.log(`child exiting: failed!`, err);
            callback(false)
            // do some cleanup
        })

        child.on('childProcess error', function (err) {
            console.log('error', err)
            if (invoked) return;
            invoked = true;
            callback(err);
        });
        // listen for errors as they may prevent the exit event from firing
        process.on('error', function (err) {
            console.log('process.on error', err)
            if (invoked) return;
            invoked = true;
            callback(err);
        });

        // execute the callback once the process has finished running
        child.on('childProcess exit', function (code) {
            console.log('exit', code)
            if (invoked) return;
            invoked = true;
            var err = code === 0 ? null : new Error('exit code ' + code);
            callback(err);
        });

    },
    prepare_anomaly_data: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try { 
            // console.log('process.cwd()', process.cwd());
            // console.log('body', body);
            anomali.runScript(path.join(process.cwd(), '/nodes/sandbox/ai_anomali.js'), 'nodes', function (err) {
                if (err == 0) { 
                    console.log('finished running backtest');
                    getResp = {
                        error: false,
                        openReports: true,
                        data: {
                            status: 'backtest success!',
                            time: new Date().toISOString(),
                        }
                    };
                    resolve({...getResp, });
                    return
                } else if (err == 1) {
                    console.log('backtest task is in progress');
                    getResp = {
                        error: false,
                        openReports: false,
                        data: {
                            status: 'backtest task is in progress',
                            time: new Date().toISOString(),
                        }
                    };
                    resolve({...getResp, });
                    return
                    // throw err
                } else {
                    console.log('error in run script', err);
                    getResp = {
                        error: true,
                        errCode: err,
                        time: new Date().toISOString(),
                    };
                    resolve({...getResp, });
                    return
                };
            }, io);
            } catch (e) {
                console.log('anomali prepare_anomaly_data error', e)
                reject({ data: false });
            }
        });
    },
    get_anomaly_list: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try { 
                let sql_query = `
                        SELECT * 
                        FROM sandbox_binance_anomaly
                        WHERE 1 = 1
                        -- AND reason_combined NOT IN ('normal')
                        -- ORDER BY anomaly_score DESC;
                `;
                let resp = sqlClient.prepare(sql_query).all();
                resolve(resp);
            } catch (e) {
                console.log('anomali get_anomaly_list error', e)
                reject({ data: false });
            }
        });
    },
    get_klines: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try {
                const { intervals, battleInterval = "1m", candleCounts = 24} = JSON.parse(body);
                const { interval, symbol } = query;
                // console.log('get_klines', interval);
                let sql_query;
                if (battleInterval) {
                    if (interval) {
                        if (interval !== 'funding') {
                            sql_query = `
                            SELECT symbol, data 
                            FROM sandbox_binance_klines_${interval.toString()}
                            WHERE 1 = 1;
                        `;
                        } else {
                            sql_query = `
                            SELECT symbol, data 
                            FROM sandbox_binance_fundingrates
                            WHERE 1 = 1;
                        `;
                        }
                    } else {
                        sql_query = `
                            SELECT symbol, data 
                            FROM sandbox_binance_klines_${battleInterval.toString()}
                            WHERE 1 = 1;
                        `;
                    }
                    if (symbol) {
                        sql_query += ` AND symbol = '${symbol}'`;
                    }
                    // console.log('sql_query', sql_query);
                    let resp = sqlClient.prepare(sql_query).all();
                    resolve(resp);
                } else {
                    reject({ data: false });
                }
            } catch (e) {
                console.log('get_klines error', e)
                reject({ data: false });
            }
        });
    }
};

const klineArr2Json = adata => {
    if (Array.isArray(adata)) {
        var time = adata[0];
        var timeTR = new Date(new Date(adata[0]) - (new Date().getTimezoneOffset() * 60000)).toISOString()
        var timeISO = new Date(adata[0]);
        var open = Number(adata[1])
        var high = Number(adata[2])
        var low = Number(adata[3])
        var close = Number(adata[4])
        var volume = Number(adata[5])
        var closeTime = (adata[6])
        var closeTimeISO = new Date(adata[6])
        var assetVolume = Number(adata[7])
        var trades = Number(adata[8])
        var color = close - open >= 0 ? 'blue' : 'red';
        var eventTime = Number(adata[12]) ? new Date(new Date(adata[12]) - (new Date().getTimezoneOffset() * 60000)).toISOString() : null || null;
        var src = Number(adata[11]) == "1" ? 'ticker' : 'kline';
        return {
            src, 
            time, open, high, low, close, volume,
            closeTimeISO, assetVolume, trades, color,
            eventTime, timeTR, timeISO, 
        }
    } else return {}
}

// Function to convert interval string to milliseconds
const intervalToMilliseconds = (interval) => {
    const intervalMap = {
        '1s': 1000,
        '1m': 60000,
        '3m': 180000,
        '5m': 300000,
        '15m': 900000,
        '30m': 1800000,
        '1h': 3600000,
        '2h': 7200000,
        '4h': 14400000,
        '6h': 21600000,
        '8h': 28800000,
        '12h': 43200000,
        '1d': 86400000,
        '3d': 259200000,
        '1w': 604800000,
        '1M': 2592000000
    };
    return intervalMap[interval] || 60000; // default to 1m if not found
};

const KlineTicker = (adata, cbType = '') => {
    //https://github.com/binance/binance-spot-api-docs/blob/master/web-socket-streams.md#klinecandlestick-streams-for-utc
    var time = adata.t;
    var timeTR = new Date(new Date(adata.t) - (new Date().getTimezoneOffset() * 60000)).toISOString()
    var timeISO = new Date(adata.t);
    var open = Number(adata.o)
    var high = Number(adata.h)
    var low = Number(adata.l)
    var close = Number(adata.c)
    var volume = Number(adata.v)
    // var closeTime = new Date(adata.T)
    var closeTimeISO = new Date(adata.T)
    var closeTime = adata.T
    var assetVolume = Number(adata.q)
    var trades = Number(adata.n)

    var takerVolume = Number(adata.V);
    var takerAssetVolume = Number(adata.Q);
    var barClosed = adata.x;
    var eventTime = adata.E;
    var eventTimeTR = eventTime ? new Date(new Date(eventTime) - (new Date().getTimezoneOffset() * 60000)).toISOString() : null
    

    var src = 'Ticker'; 
    var color = close - open >= 0 ? 'blue' : 'red';
    if (cbType == 'json') {
        return {
            src, 
            time, open, high, low, close, volume,
            closeTimeISO, assetVolume, trades, color,
            timeTR, timeISO, eventTime, barClosed
            // src, eventTime,
            // time, timeTR, timeISO,
            // open, high, low, close, volume,
            // closeTimeISO, assetVolume, trades, color
        }
    } else {
        let arr = [time, open.toString(), high.toString(), low.toString(), close.toString(), volume.toString(), closeTime,
            assetVolume.toString(), trades, takerVolume.toString(), takerAssetVolume.toString(), "1", 
            //color, timeTR, timeISO, 
            eventTime];
        return arr;
    }

}
